package com.ruoyi.video.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.video.config.IceClientAKConfig;
import com.ruoyi.video.dto.CreateEditingProjectRequestDTO;
import com.ruoyi.video.dto.SubmitProjectExportRequestDTO;
import com.ruoyi.video.service.IVideoProjectService;

import lombok.extern.slf4j.Slf4j;

/**
 * 云剪辑工程 服务层实现
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class VideoProjectServiceImpl implements IVideoProjectService {

    @Autowired
    private IAcsClient acsClient;

    @Autowired
    private IceClientAKConfig iceConfig;
    
    // 服务版本常量
    private static final String API_VERSION = "2020-11-09";

    @Override
    public String listEditingProjects(String keyword, String status, String nextToken, Integer maxResults) throws Exception {
        validateAcsClient();

        // 验证Status参数的有效性
        if (status != null && !status.trim().isEmpty()) {
            if (!isValidStatus(status)) {
                log.warn("传入的Status参数无效: {}, 将忽略此参数", status);
                status = null; // 忽略无效的status参数
            }
        }

        CommonRequest request = createCommonRequest("ListEditingProjects", MethodType.POST);
        addParameterIfNotNull(request, "Keyword", keyword);
        addParameterIfNotNull(request, "Status", status);
        addParameterIfNotNull(request, "NextToken", nextToken);
        addParameterIfNotNull(request, "MaxResults", maxResults);

        return executeRequest(request);
    }

    @Override
    public String deleteEditingProjects(String projectIds) throws Exception {
        validateAcsClient();
        if (projectIds == null || projectIds.trim().isEmpty()) {
            throw new ServiceException("要删除的工程ID (ProjectIds) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("DeleteEditingProjects", MethodType.POST);
        request.putQueryParameter("ProjectIds", projectIds);
        
        return executeRequest(request);
    }

    @Override
    public String createEditingProject(CreateEditingProjectRequestDTO requestDTO) throws Exception {
        validateAcsClient();
        if (requestDTO == null) {
            throw new ServiceException("创建工程请求数据不能为空");
        }
        
        CommonRequest request = createCommonRequest("CreateEditingProject", MethodType.POST);
        request.putQueryParameter("Title", requestDTO.getTitle());
        addParameterIfNotNull(request, "Description", requestDTO.getDescription());
        addParameterIfNotNull(request, "Timeline", requestDTO.getTimeline());
        addParameterIfNotNull(request, "CoverURL", requestDTO.getCoverURL());
        addParameterIfNotNull(request, "TemplateId", requestDTO.getTemplateId());
        addParameterIfNotNull(request, "ClipsParam", requestDTO.getClipsParam());
        addParameterIfNotNull(request, "TemplateType", requestDTO.getTemplateType());
        addParameterIfNotNull(request, "MaterialMaps", requestDTO.getMaterialMaps());
        addParameterIfNotNull(request, "BusinessConfig", requestDTO.getBusinessConfig());
        addParameterIfNotNull(request, "ProjectType", requestDTO.getProjectType());
        
        return executeRequest(request);
    }

    @Override
    public String getEditingProject(String projectId, String requestSource) throws Exception {
        validateAcsClient();
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new ServiceException("要查询的工程ID (ProjectId) 不能为空。");
        }
        
        CommonRequest request = createCommonRequest("GetEditingProject", MethodType.POST);
        request.putQueryParameter("ProjectId", projectId);
        addParameterIfNotNull(request, "RequestSource", requestSource);
        
        return executeRequest(request);
    }
    
    /**
     * 验证AcsClient是否初始化
     */
    private void validateAcsClient() {
        if (acsClient == null) {
            throw new IllegalStateException("IAcsClient 未初始化，请检查配置中的阿里云访问密钥(AccessKey ID/Secret)和地区(Region)设置。");
        }
    }
    
    /**
     * 创建通用请求对象
     */
    private CommonRequest createCommonRequest(String action, MethodType method) {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(method);
        request.setSysDomain(iceConfig.getEndpoint());
        request.setSysVersion(API_VERSION);
        request.setSysAction(action);
        return request;
    }
    
    /**
     * 添加非空参数到请求
     */
    private void addParameterIfNotNull(CommonRequest request, String key, Object value) {
        if (value != null) {
            if (value instanceof String && ((String) value).isEmpty()) {
                return;
            }
            request.putQueryParameter(key, value.toString());
        }
    }
    
    /**
     * 验证Status参数是否有效
     */
    private boolean isValidStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            return true; // 空值是有效的
        }

        // 阿里云ICE API支持的Status有效值
        String[] validStatuses = {"Draft", "Editing", "Producing", "Produced", "ProduceFailed"};
        for (String validStatus : validStatuses) {
            if (validStatus.equalsIgnoreCase(status.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 执行请求并处理响应
     */
    private String executeRequest(CommonRequest request) throws Exception {
        try {
            CommonResponse response = acsClient.getCommonResponse(request);
            String data = response.getData();

            if (response.getHttpStatus() != 200) {
                log.error("请求失败，HTTP状态码: {}, 响应数据: {}", response.getHttpStatus(), data);

                // 检查是否是参数无效错误，如果是则返回空结果
                if (data != null && data.contains("InvalidParameter")) {
                    log.warn("参数无效，返回空结果");
                    return createEmptyListResponse();
                }

                throw new Exception("请求失败: " + data);
            }

            return data;
        } catch (Exception e) {
            log.error("执行请求时发生异常: {}", e.getMessage(), e);

            // 如果是参数相关的异常，返回空结果而不是抛出异常
            if (e.getMessage() != null &&
                (e.getMessage().contains("InvalidParameter") ||
                 e.getMessage().contains("specified parameter") ||
                 e.getMessage().contains("is not valid"))) {
                log.warn("参数相关异常，返回空结果: {}", e.getMessage());
                return createEmptyListResponse();
            }

            throw e;
        }
    }

    @Override
    public String submitProjectExportJob(SubmitProjectExportRequestDTO requestDTO) throws Exception {
        validateAcsClient();
        if (requestDTO == null) {
            throw new ServiceException("导出任务请求数据不能为空");
        }

        // 验证必填参数：ProjectId 和 Timeline 二选一
        if ((requestDTO.getProjectId() == null || requestDTO.getProjectId().trim().isEmpty()) &&
            (requestDTO.getTimeline() == null || requestDTO.getTimeline().trim().isEmpty())) {
            throw new ServiceException("ProjectId 和 Timeline 参数二选一必填");
        }

        // 验证 OutputMediaConfig 必填
        String outputMediaConfig = buildOutputMediaConfig(requestDTO);
        if (outputMediaConfig == null || outputMediaConfig.trim().isEmpty()) {
            throw new ServiceException("OutputMediaConfig 不能为空，请提供OSS存储配置");
        }

        CommonRequest request = createCommonRequest("SubmitProjectExportJob", MethodType.POST);

        // 添加必填和可选参数
        addParameterIfNotNull(request, "ProjectId", requestDTO.getProjectId());
        addParameterIfNotNull(request, "Timeline", requestDTO.getTimeline());
        addParameterIfNotNull(request, "ExportType", requestDTO.getExportType());
        request.putQueryParameter("OutputMediaConfig", outputMediaConfig);

        // 构建用户自定义数据
        String userData = buildUserData(requestDTO);
        addParameterIfNotNull(request, "UserData", userData);

        return executeRequest(request);
    }

    /**
     * 构建 OutputMediaConfig JSON 字符串
     */
    private String buildOutputMediaConfig(SubmitProjectExportRequestDTO requestDTO) {
        // 如果已经提供了完整的 JSON 字符串，直接使用
        if (requestDTO.getOutputMediaConfig() != null && !requestDTO.getOutputMediaConfig().trim().isEmpty()) {
            return requestDTO.getOutputMediaConfig();
        }

        // 如果提供了便捷字段，构建 JSON
        if (requestDTO.getBucket() != null && !requestDTO.getBucket().trim().isEmpty()) {
            JSONObject config = new JSONObject();
            config.put("Bucket", requestDTO.getBucket());

            if (requestDTO.getPrefix() != null && !requestDTO.getPrefix().trim().isEmpty()) {
                config.put("Prefix", requestDTO.getPrefix());
            }
            if (requestDTO.getWidth() != null && requestDTO.getWidth() > 0) {
                config.put("Width", requestDTO.getWidth());
            }
            if (requestDTO.getHeight() != null && requestDTO.getHeight() > 0) {
                config.put("Height", requestDTO.getHeight());
            }

            return config.toJSONString();
        }

        return null;
    }

    /**
     * 构建 UserData JSON 字符串
     */
    private String buildUserData(SubmitProjectExportRequestDTO requestDTO) {
        // 如果已经提供了完整的 JSON 字符串，直接使用
        if (requestDTO.getUserData() != null && !requestDTO.getUserData().trim().isEmpty()) {
            return requestDTO.getUserData();
        }

        // 如果提供了便捷字段，构建 JSON
        if (requestDTO.getNotifyAddress() != null && !requestDTO.getNotifyAddress().trim().isEmpty()) {
            JSONObject userData = new JSONObject();
            userData.put("NotifyAddress", requestDTO.getNotifyAddress());
            return userData.toJSONString();
        }

        return null;
    }

    /**
     * 创建空的列表响应
     */
    private String createEmptyListResponse() {
        return "{\"RequestId\":\"empty-result\",\"ProjectList\":[],\"MaxResults\":0,\"NextToken\":\"\"}";
    }
}