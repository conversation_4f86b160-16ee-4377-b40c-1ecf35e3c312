package com.ruoyi.video.service;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.dto.MediaDTO;
import com.ruoyi.video.utils.MediaUtils;

/**
 * 智能媒体服务(ICE)媒资管理 服务层接口
 * <p>
 * 定义了与媒资信息相关的业务操作，例如获取媒资详情。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IMediaService {

    /**
     * 调用阿里云ICE API获取单个媒资的详细信息。
     * <p>
     * 必须提供 MediaId 或 InputURL 两者之一。
     * </p>
     *
     * @param mediaId            可选。IMS 媒资 ID。
     * @param inputURL           可选。待查询的媒资在相应系统中的地址。
     * @param outputType         可选。返回值中媒资文件地址的类型: "oss" (默认) 或 "cdn"。
     * @param returnDetailedInfo 可选。配置特定字段是否返回详细信息，JSON格式字符串。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了指定的媒资详细信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String getMediaInfo(String mediaId, String inputURL, String outputType, String returnDetailedInfo) throws Exception;

    /**
     * 调用阿里云ICE API批量获取多个媒资的详细信息。
     * <p>
     * 支持传入多个mediaId，以逗号分隔。
     * </p>
     *
     * @param mediaIds     可选。所有待查询的媒资ID，以逗号分隔，例如：mediaId1,mediaId2。
     * @param additionType 可选。批量查询的媒资额外信息，默认只返回BasicInfo，额外文件信息内容包括：
     *                     -FileInfo
     *                     -DynamicMetaData
     *                     多个类型用逗号分隔，例如：FileInfo,DynamicMetaData。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了多个媒资的详细信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String batchGetMediaInfos(String mediaIds, String additionType) throws Exception;

    /**
     * 调用阿里云ICE API获取剪辑工程关联素材。
     * <p>
     * 获取当前工程绑定的所有素材，包括媒资信息、直播流关联素材等。
     * 这是一个通用接口，可供模板工厂等其他模块使用。
     * </p>
     *
     * @param projectId 云剪辑工程ID。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了工程关联的所有素材信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String getEditingProjectMaterials(String projectId) throws Exception;

    /**
     * 调用阿里云ICE API增加剪辑工程关联素材。
     * <p>
     * 将一个或多个素材添加到剪辑工程中。
     * </p>
     *
     * @param projectId    云剪辑工程 ID。
     * @param materialMaps 素材 ID，多个素材以逗号（,）分隔；每种类型最多支持 10 个素材 ID，JSON格式字符串。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了增加素材后的信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String addEditingProjectMaterials(String projectId, String materialMaps) throws Exception;

    /**
     * 已上传的媒资文件注册到媒资库中。
     * <p>
     * 通过调用阿里云ICE的注册接口，将已上传的媒资文件注册到媒资库中。
     * </p>
     *
     * @param requestDTO 包含媒资注册所需参数的数据传输对象。
     * @return 阿里云API返回的原始JSON格式字符串，其中包含了注册后的媒资信息。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     */
    String registerMediaInfo(MediaDTO requestDTO) throws Exception;
    /**
     * 一体化文件上传和媒资注册。
     * <p>
     * 集成文件上传到OSS和媒资注册流程，前端只需一次调用即可完成全流程。
     * OSS路径格式：oss://桶名/ice/MediaLibrary/{category}/{date}/{timestamp}_{filename}
     * </p>
     *
     * @param file     待上传的文件
     * @param category 媒资分类，如果为空则默认为"general"
     * @return 包含媒资ID、OSS路径等信息的结果
     * @throws Exception 当上传或注册失败时抛出
     */
    AjaxResult uploadAndRegisterMedia(MultipartFile file, String category) throws Exception;

    /**
     * 列出媒资基础信息
     * <p>
     * 获取当前云剪辑工程绑定的所有素材，包括媒资信息、直播流关联素材等。
     * 这是一个通用接口，可供模板工厂等其他模块使用。
     * </p>
     * 
     * @param StartTime            utcCreated（创建时间）的开始时间
     * @param EndTime              utcCreated（创建时间）的结束时间
     * @param MediaType            媒资类型，取值范围 image,video,audio,text
     * @param BusinessType         媒资业务类型， 取值范围：syvtutkes 字幕,watermark 水印,opening
     *                             开场,ending 结尾/片尾,general 通用
     * @param Source               媒资来源，取值范围：oss vod live general
     * @param Status               媒资状态，取值范围：lnit 初始状态,Preparing 源文件准备中,PrepareFail
     *                             原文件准备失败 Normal 正常
     * @param NextToken            用来表示当前调用返回读取到的位置空代表数据已经读取完毕
     * @param MaxResults           本次请求所返回的最大记录条数 默认10
     * @param SortBy               排序 默认倒序
     * @param IncludeFileBasicInfo 若果为true 返回值中包含文件基础信息
     * @param MediaId              媒资ID
     * @return 返回一个包含媒资基础信息的AjaxResult对象。
     * @throws Exception 当API调用失败或发生其他异常时抛出。
     * @see MediaUtils
     * @see MediaDTO
     * @see IMediaService#listMediaBasicInfo(MediaUtils)
     * <AUTHOR>
     * @date 2025-07-12
     */
    Object listMediaBasicInfo(MediaDTO dto) throws Exception;
}