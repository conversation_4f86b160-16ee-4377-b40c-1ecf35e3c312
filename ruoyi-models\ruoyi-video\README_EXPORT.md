# 云剪辑工程导出接口使用说明

## 接口概述

云剪辑工程导出接口 `SubmitProjectExportJob` 用于将阿里云ICE云剪辑工程导出为视频文件或Adobe PR工程文件。

## 接口地址

```
POST /video/project/export
```

## 请求参数

### 基本参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectId | String | 否 | 云剪辑工程ID（与timeline二选一） |
| timeline | String | 否 | 云剪辑任务时间线JSON（与projectId二选一） |
| exportType | String | 否 | 导出类型，默认BaseTimeline |
| outputMediaConfig | String | 是 | 输出配置JSON字符串 |
| userData | String | 否 | 用户自定义数据JSON字符串 |

### 便捷参数（用于自动构建JSON）

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| bucket | String | 否 | OSS存储桶名称 |
| prefix | String | 否 | 文件路径前缀 |
| width | Integer | 否 | 视频宽度 |
| height | Integer | 否 | 视频高度 |
| notifyAddress | String | 否 | 回调通知地址 |

## 请求示例

### 示例1：使用工程ID导出

```json
{
  "projectId": "*****67ae06542b9b93e0d1c387*****",
  "exportType": "BaseTimeline",
  "bucket": "my-video-bucket",
  "prefix": "exports/",
  "width": 1920,
  "height": 1080,
  "notifyAddress": "https://my-domain.com/callback"
}
```

### 示例2：使用时间线导出

```json
{
  "timeline": "{\"VideoTracks\":[{\"VideoTrackClips\":[{\"MediaId\":\"****4d7cf14dc7b83b0e801c****\"},{\"MediaId\":\"****4d7cf14dc7b83b0e801c****\"}]}]}",
  "exportType": "BaseTimeline",
  "outputMediaConfig": "{\"Bucket\": \"my-video-bucket\", \"Prefix\": \"exports/\", \"Width\": 1920, \"Height\": 1080}",
  "userData": "{\"NotifyAddress\":\"https://my-domain.com/callback\",\"CustomKey\":\"CustomValue\"}"
}
```

### 示例3：导出为Adobe PR工程

```json
{
  "projectId": "*****67ae06542b9b93e0d1c387*****",
  "exportType": "AdobePremierePro",
  "bucket": "my-video-bucket",
  "prefix": "pr-exports/"
}
```

## 响应示例

### 成功响应

```json
{
  "code": 200,
  "msg": "提交导出任务成功",
  "data": {
    "RequestId": "******11-DB8D-4A9A-875B-275798******",
    "JobId": "****cdb3e74639973036bc84****"
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "msg": "提交导出任务失败: ProjectId 和 Timeline 参数二选一必填"
}
```

## 注意事项

1. **必填参数验证**：ProjectId 和 Timeline 必须提供其中一个
2. **OSS配置**：必须提供有效的OSS存储配置
3. **区域限制**：Adobe PR导出仅在上海、北京、杭州、深圳区域支持
4. **权限要求**：需要 `ice:SubmitProjectExportJob` 权限
5. **回调配置**：建议配置回调地址以获取任务完成通知

## 导出类型说明

- **BaseTimeline**：导出为视频文件（MP4等格式）
- **AdobePremierePro**：导出为Adobe Premiere Pro工程文件

## 输出配置说明

OutputMediaConfig 支持以下字段：
- `Bucket`：OSS存储桶名称（必填）
- `Prefix`：文件路径前缀（可选）
- `Width`：视频宽度（可选，自动估算）
- `Height`：视频高度（可选，自动估算）
