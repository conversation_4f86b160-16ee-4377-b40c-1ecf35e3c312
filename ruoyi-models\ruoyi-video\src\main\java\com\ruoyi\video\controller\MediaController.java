package com.ruoyi.video.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.dto.MediaDTO;
import com.ruoyi.video.service.IMediaService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 智能媒体服务(ICE)媒资管理 前端控制器
 * <p>
 * 提供RESTful API，用于查询媒资信息、上传注册媒资、管理剪辑工程关联素材等操作。
 * </p>
 *
 * <h3>媒资上传和注册流程：</h3>
 * <p>
 * <strong>推荐方式（一体化）：</strong>
 * </p>
 * <ol>
 * <li>调用 POST /video/media/uploadAndRegister 直接上传文件并注册媒资</li>
 * </ol>
 *
 * <p>
 * <strong>分步方式（兼容旧版）：</strong>
 * </p>
 * <ol>
 * <li>调用 POST /video/media/createUploadMedia 获取上传凭证（已废弃）</li>
 * <li>调用 POST /video/media/registerMediaInfo 注册媒资</li>
 * </ol>
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/video/media")
@Tag(name = "【媒资管理】")
public class MediaController {

    @Autowired
    private IMediaService mediaService;

    /**
     * 获取单个媒资的详细信息
     */
    @Operation(summary = "获取单个媒资的详细信息")
    @GetMapping("/info")
    public AjaxResult getMediaInfo(
            @RequestParam(required = false) String mediaId,
            @RequestParam(required = false) String inputURL,
            @RequestParam(required = false, defaultValue = "oss") String outputType,
            @RequestParam(required = false) String returnDetailedInfo) {
        try {
            String result = mediaService.getMediaInfo(mediaId, inputURL, outputType, returnDetailedInfo);
            return AjaxResult.success("获取媒资信息成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("获取媒资信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取媒资信息
     */
    @Operation(summary = "批量获取媒资信息")
    @GetMapping("/batchGetMediaInfos")
    public AjaxResult batchGetMediaInfos(
            @RequestParam(required = true) String mediaIds,
            @RequestParam(required = false) String additionType) {
        try {
            String result = mediaService.batchGetMediaInfos(mediaIds, additionType);
            return AjaxResult.success("批量获取媒资信息成功", JSON.parse(result));
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("批量获取媒资信息失败: " + e.getMessage());
        }
    }

    /**
     * 列出媒资基础信息
     */
    @Operation(summary = "列出媒资基础信息")
    @GetMapping("/listMediaBasicInfo")
    public AjaxResult listMediaBasicInfo(MediaDTO dto) {
        try {
            return AjaxResult.success("获取媒资基础信息成功", mediaService.listMediaBasicInfo(dto));
        } catch (Exception e) {
            return AjaxResult.error("获取媒资基础信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取云剪辑工程关联素材
     */
    @Operation(summary = "获取云剪辑工程关联素材")
    @GetMapping("/project/{projectId}/materials")
    public AjaxResult getEditingProjectMaterials(
            @PathVariable @Parameter(name = "projectId", description = "云剪辑工程ID", in = ParameterIn.PATH) String projectId) {
        if (projectId == null || projectId.trim().isEmpty()) {
            return AjaxResult.error("工程ID不能为空");
        }
        try {
            String result = mediaService.getEditingProjectMaterials(projectId);
            return AjaxResult.success("获取剪辑工程关联素材成功", JSON.parse(result));
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("工程不存在")) {
                return AjaxResult.error("指定的云剪辑工程不存在，请检查工程ID是否正确")
                        .put("projectId", projectId)
                        .put("suggestion", "请先通过工程列表接口确认工程是否存在");
            } else if (errorMessage != null && errorMessage.contains("参数无效")) {
                return AjaxResult.error("工程ID格式无效，请检查ID格式是否正确")
                        .put("projectId", projectId);
            } else {
                return AjaxResult.error("获取剪辑工程关联素材失败: " + errorMessage);
            }
        }
    }

    /**
     * 增加剪辑工程关联素材
     */
    @Operation(summary = "增加剪辑工程关联素材")
    @PostMapping("/addProjectMaterials")
    public AjaxResult addEditingProjectMaterials(
            @RequestParam(required = true) String projectId,
            @RequestParam(required = true) String materialMaps) {
        try {
            String result = mediaService.addEditingProjectMaterials(projectId, materialMaps);
            return AjaxResult.success("增加剪辑工程关联素材成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("增加剪辑工程关联素材失败: " + e.getMessage());
        }
    }

    /**
     * 媒资上传并注册 - 一体化接口
     */
    @Operation(summary = "媒资文件上传并注册（一体化）")
    @PostMapping(value = "/uploadAndRegister", consumes = "multipart/form-data")
    public AjaxResult uploadAndRegisterMedia(
            @Parameter(name = "file", description = "要上传的媒体文件", required = true)
            @RequestParam("file") MultipartFile file,

            @Parameter(name = "category", description = "媒资分类（默认为 general）", required = false)
            @RequestParam(value = "category", required = false, defaultValue = "general") String category) {

        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空，请确保使用multipart/form-data格式发送请求");
        }
        try {
            return mediaService.uploadAndRegisterMedia(file, category);
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("媒资上传并注册失败: " + e.getMessage());
        }
    }
}